<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live YT Music Lyrics</title>
    <style>
        :root {
            --primary-color: #ff6b35;
            --primary-gradient: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3a3a3a;
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --text-muted: rgba(255, 255, 255, 0.6);
            --border-color: rgba(255, 255, 255, 0.1);
            --success-color: #00ff88;
            --error-color: #ff4444;
            --warning-color: #ffaa00;
            --info-color: #007bff;
        }

        * {
            box-sizing: border-box;
        }

        body {
            width: 380px;
            min-height: 500px;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 13px;
            line-height: 1.4;
        }

        .header {
            background: var(--primary-gradient);
            padding: 16px 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .header p {
            margin: 4px 0 0 0;
            opacity: 0.9;
            font-size: 11px;
        }

        .connection-status {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            box-shadow: 0 0 4px rgba(0, 255, 136, 0.5);
        }

        .connection-status.disconnected {
            background: var(--error-color);
            box-shadow: 0 0 4px rgba(255, 68, 68, 0.5);
        }

        .connection-status.connecting {
            background: var(--warning-color);
            box-shadow: 0 0 4px rgba(255, 170, 0, 0.5);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        .content {
            padding: 16px;
            max-height: calc(100vh - 120px);
            overflow-y: auto;
        }

        /* Current Song Section */
        .current-song {
            background: rgba(255, 107, 53, 0.08);
            border: 1px solid rgba(255, 107, 53, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            position: relative;
        }

        .current-song h3 {
            margin: 0 0 8px 0;
            color: var(--primary-color);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .song-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .album-art {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .song-details {
            flex: 1;
            min-width: 0;
        }

        .song-title {
            font-weight: 600;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 13px;
        }

        .song-artist {
            color: var(--text-secondary);
            font-size: 11px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .song-duration {
            color: var(--text-muted);
            font-size: 10px;
            margin-top: 2px;
        }

        .dynamic-theme-preview {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: var(--primary-color);
        }

        .controls {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .control-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .control-label {
            font-size: 13px;
            flex: 1;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #444;
            transition: 0.3s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: #ff6b35;
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }

        .btn {
            background: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.2s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            text-align: center;
        }

        .status.success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            color: #00ff00;
        }

        .status.error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff4444;
        }

        .status.info {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.3);
            color: #007bff;
        }

        .footer {
            padding: 15px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            font-size: 11px;
            opacity: 0.7;
        }

        .footer a {
            color: #ff6b35;
            text-decoration: none;
        }

        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 107, 53, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b35;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>🎵 Live YT Music Lyrics</h1>
        <p>Real-time synchronized lyrics overlay</p>
    </div>

    <div class="content">
        <div class="current-song">
            <h3>Current Song</h3>
            <div class="song-info" id="song-info">
                <div class="song-title" id="song-title">No song detected</div>
                <div class="song-artist" id="song-artist">Open YouTube Music to start</div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <span class="control-label">Enable Lyrics</span>
                <label class="switch">
                    <input type="checkbox" id="toggle-lyrics" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="control-group">
                <span class="control-label">Auto-scroll</span>
                <label class="switch">
                    <input type="checkbox" id="toggle-autoscroll" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="control-group">
                <span class="control-label">Show overlay</span>
                <label class="switch">
                    <input type="checkbox" id="toggle-overlay" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <button class="btn" id="refresh-lyrics">
                <span id="refresh-text">🔄 Refresh Lyrics</span>
                <span class="loading hidden" id="refresh-loading"></span>
            </button>
        </div>

        <div class="status hidden" id="status"></div>
    </div>

    <div class="footer">
        Made with ❤️ by Sukarth Acharya<br>
        <a href="#" id="github-link">View on GitHub</a> |
        <a href="https://github.com/sukarth/liveytmusiclyrics" target="_blank">Open Source</a>
    </div>

    <script src="popup.js"></script>
</body>

</html>