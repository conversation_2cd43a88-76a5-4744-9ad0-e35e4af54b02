<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live YT Music Lyrics</title>
    <style>
        :root {
            /* Theme colors matching content.js defaults */
            --primary-color: #ff0000;
            --primary-light: #ff4444;
            --primary-dark: #cc0000;
            --primary-gradient: linear-gradient(90deg, #ff0000 0%, #ff4444 100%);

            /* Alpha variants for transparency effects */
            --primary-alpha-10: rgba(255, 0, 0, 0.1);
            --primary-alpha-15: rgba(255, 0, 0, 0.15);
            --primary-alpha-20: rgba(255, 0, 0, 0.2);
            --primary-alpha-30: rgba(255, 0, 0, 0.3);
            --primary-alpha-40: rgba(255, 0, 0, 0.4);
            --primary-alpha-50: rgba(255, 0, 0, 0.5);

            /* Background colors */
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3a3a3a;
            --bg-card: #252525;

            /* Text colors */
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --text-muted: rgba(255, 255, 255, 0.6);
            --text-disabled: rgba(255, 255, 255, 0.4);

            /* Border and divider colors */
            --border-color: rgba(255, 255, 255, 0.1);
            --border-color-light: rgba(255, 255, 255, 0.05);
            --divider-color: rgba(255, 255, 255, 0.08);

            /* Status colors */
            --success-color: #00ff88;
            --error-color: #ff4444;
            --warning-color: #ffaa00;
            --info-color: #007bff;

            /* Shadows and effects */
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
            --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.3);

            /* Transitions */
            --transition-fast: 0.15s ease-out;
            --transition-medium: 0.3s ease-out;
            --transition-slow: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            width: 380px;
            min-height: 500px;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 13px;
            line-height: 1.4;
        }

        .header {
            background: var(--primary-gradient);
            padding: 16px 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .header p {
            margin: 4px 0 0 0;
            opacity: 0.9;
            font-size: 11px;
        }

        .connection-status {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            box-shadow: 0 0 4px rgba(0, 255, 136, 0.5);
        }

        .connection-status.disconnected {
            background: var(--error-color);
            box-shadow: 0 0 4px rgba(255, 68, 68, 0.5);
        }

        .connection-status.connecting {
            background: var(--warning-color);
            box-shadow: 0 0 4px rgba(255, 170, 0, 0.5);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        .content {
            padding: 16px;
            max-height: calc(100vh - 120px);
            overflow-y: auto;
        }

        /* Current Song Section */
        .current-song {
            background: var(--primary-alpha-10);
            border: 1px solid var(--primary-alpha-20);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            position: relative;
        }

        .current-song h3 {
            margin: 0 0 8px 0;
            color: var(--primary-color);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .song-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .album-art {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .song-details {
            flex: 1;
            min-width: 0;
        }

        .song-title {
            font-weight: 600;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 13px;
        }

        .song-artist {
            color: var(--text-secondary);
            font-size: 11px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .song-duration {
            color: var(--text-muted);
            font-size: 10px;
            margin-top: 2px;
        }

        .dynamic-theme-preview {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: var(--primary-color);
        }

        .controls {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .control-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .control-label {
            font-size: 13px;
            flex: 1;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #444;
            transition: 0.3s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: var(--primary-color);
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }

        .btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            transition: var(--transition-fast);
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--primary-alpha-30);
        }

        .btn:active {
            transform: translateY(0);
        }

        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            text-align: center;
        }

        .status.success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            color: #00ff00;
        }

        .status.error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff4444;
        }

        .status.info {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.3);
            color: #007bff;
        }

        .footer {
            padding: 15px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            font-size: 11px;
            opacity: 0.7;
        }

        .footer a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid var(--primary-alpha-30);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 16px;
        }

        .btn-secondary {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-card);
            border-color: var(--primary-alpha-30);
            box-shadow: 0 2px 8px var(--primary-alpha-20);
        }

        /* Advanced Settings Panel */
        .settings-panel {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-top: 16px;
            overflow: hidden;
            transition: var(--transition-medium);
        }

        .settings-panel.collapsed {
            max-height: 0;
            border: none;
            margin-top: 0;
        }

        .settings-panel.expanded {
            max-height: 500px;
        }

        .settings-header {
            padding: 12px 16px;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .settings-content {
            padding: 16px;
        }

        .setting-group {
            margin-bottom: 16px;
        }

        .setting-group:last-child {
            margin-bottom: 0;
        }

        .setting-label {
            display: block;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 6px;
            color: var(--text-secondary);
        }

        .setting-input {
            width: 100%;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px 12px;
            color: var(--text-primary);
            font-size: 12px;
        }

        .setting-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px var(--primary-alpha-20);
        }

        .range-input {
            width: 100%;
            margin: 8px 0;
        }

        .color-input-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .color-preview {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            cursor: pointer;
        }

        .color-input {
            flex: 1;
            font-family: monospace;
        }

        /* Status and Info */
        .status {
            margin-top: 12px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            text-align: center;
            border: 1px solid transparent;
        }

        .status.success {
            background: rgba(0, 255, 136, 0.1);
            border-color: rgba(0, 255, 136, 0.3);
            color: var(--success-color);
        }

        .status.error {
            background: rgba(255, 68, 68, 0.1);
            border-color: rgba(255, 68, 68, 0.3);
            color: var(--error-color);
        }

        .status.warning {
            background: rgba(255, 170, 0, 0.1);
            border-color: rgba(255, 170, 0, 0.3);
            color: var(--warning-color);
        }

        .status.info {
            background: rgba(0, 123, 255, 0.1);
            border-color: rgba(0, 123, 255, 0.3);
            color: var(--info-color);
        }

        /* Utility Classes */
        .hidden {
            display: none !important;
        }

        .text-center {
            text-align: center;
        }

        .text-muted {
            color: var(--text-muted);
        }

        .mt-1 {
            margin-top: 4px;
        }

        .mt-2 {
            margin-top: 8px;
        }

        .mt-3 {
            margin-top: 12px;
        }

        .mb-1 {
            margin-bottom: 4px;
        }

        .mb-2 {
            margin-bottom: 8px;
        }

        .mb-3 {
            margin-bottom: 12px;
        }

        /* Footer */
        .footer {
            padding: 12px 16px;
            border-top: 1px solid var(--border-color);
            background: var(--bg-tertiary);
            font-size: 10px;
        }

        .footer a {
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition-fast);
        }

        .footer a:hover {
            color: var(--primary-light);
            text-decoration: underline;
        }

        /* Performance Monitor */
        .performance-monitor {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-top: 12px;
            font-size: 11px;
        }

        .monitor-header {
            padding: 8px 12px;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
        }

        .btn-icon {
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            transition: var(--transition-fast);
        }

        .btn-icon:hover {
            background: var(--primary-alpha-20);
            color: var(--text-primary);
        }

        .monitor-content {
            padding: 8px 12px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }

        .metric:last-child {
            margin-bottom: 0;
        }

        .metric-label {
            color: var(--text-secondary);
        }

        .metric-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .metric-value.connected {
            color: var(--success-color);
        }

        .metric-value.disconnected {
            color: var(--error-color);
        }

        .metric-value.connecting {
            color: var(--warning-color);
        }

        /* Enhanced Error Messages */
        .error-message {
            text-align: left;
        }

        .error-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .error-suggestion {
            font-size: 10px;
            opacity: 0.8;
            font-style: italic;
        }

        /* Toast Notifications */
        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 12px 16px;
            font-size: 12px;
            font-weight: 500;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform var(--transition-medium);
            box-shadow: var(--shadow-medium);
            max-width: 250px;
        }

        .toast-notification.show {
            transform: translateX(0);
        }

        .toast-success {
            border-left: 3px solid var(--success-color);
            color: var(--success-color);
        }

        .toast-error {
            border-left: 3px solid var(--error-color);
            color: var(--error-color);
        }

        .toast-warning {
            border-left: 3px solid var(--warning-color);
            color: var(--warning-color);
        }

        .toast-info {
            border-left: 3px solid var(--info-color);
            color: var(--info-color);
        }

        /* Import/Export Buttons */
        .import-export-buttons {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 11px;
            flex: 1;
        }

        /* Song Manager Panel */
        .song-manager-panel {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-top: 16px;
            overflow: hidden;
            max-height: 300px;
        }

        .song-manager-header {
            padding: 12px 16px;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            font-size: 12px;
        }

        .song-manager-content {
            max-height: 250px;
            overflow-y: auto;
        }

        .song-item {
            padding: 8px 16px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 11px;
        }

        .song-item:last-child {
            border-bottom: none;
        }

        .song-item:hover {
            background: var(--primary-alpha-10);
        }

        .song-details-mini {
            flex: 1;
            min-width: 0;
        }

        .song-title-mini {
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 2px;
        }

        .song-artist-mini {
            color: var(--text-secondary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .song-actions {
            display: flex;
            gap: 4px;
            margin-left: 8px;
        }

        .btn-mini {
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .btn-mini:hover {
            background: var(--primary-alpha-20);
            color: var(--text-primary);
        }

        .btn-mini.danger:hover {
            background: rgba(255, 68, 68, 0.2);
            color: var(--error-color);
        }

        .empty-state {
            padding: 24px 16px;
            text-align: center;
            color: var(--text-muted);
            font-size: 11px;
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="connection-status" id="connection-status"></div>
        <h1>🎵 Live YT Music Lyrics</h1>
        <p>Real-time synchronized lyrics overlay</p>
    </div>

    <div class="content">
        <!-- Current Song Section -->
        <div class="current-song">
            <h3>Current Song</h3>
            <div class="song-info" id="song-info">
                <div class="album-art" id="album-art">🎵</div>
                <div class="song-details">
                    <div class="song-title" id="song-title">No song detected</div>
                    <div class="song-artist" id="song-artist">Open YouTube Music to start</div>
                    <div class="song-duration hidden" id="song-duration"></div>
                </div>
            </div>
            <div class="dynamic-theme-preview hidden" id="dynamic-theme-preview"></div>
        </div>

        <!-- Quick Controls -->
        <div class="controls">
            <div class="control-group">
                <span class="control-label">Enable Lyrics</span>
                <label class="switch">
                    <input type="checkbox" id="toggle-lyrics" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="control-group">
                <span class="control-label">Auto-scroll</span>
                <label class="switch">
                    <input type="checkbox" id="toggle-autoscroll" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="control-group">
                <span class="control-label">Dynamic Theme Colors</span>
                <label class="switch">
                    <input type="checkbox" id="toggle-dynamic-theme">
                    <span class="slider"></span>
                </label>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn" id="refresh-lyrics">
                <span id="refresh-text">🔄 Refresh Lyrics</span>
                <span class="loading hidden" id="refresh-loading"></span>
            </button>

            <button class="btn btn-secondary" id="show-settings">
                ⚙️ Advanced Settings
            </button>

            <button class="btn btn-secondary" id="show-song-manager">
                📋 Manage Song Settings
            </button>

            <div class="import-export-buttons">
                <button class="btn btn-secondary btn-small" id="export-settings">
                    📤 Export
                </button>
                <button class="btn btn-secondary btn-small" id="import-settings">
                    📥 Import
                </button>
                <input type="file" id="import-file" accept=".json" style="display: none;">
            </div>
        </div>

        <!-- Status Display -->
        <div class="status hidden" id="status"></div>

        <!-- Performance Monitor (optional, can be toggled) -->
        <div class="performance-monitor hidden" id="performance-monitor">
            <div class="monitor-header">
                <span>📊 Performance</span>
                <button class="btn-icon" id="toggle-monitor">×</button>
            </div>
            <div class="monitor-content">
                <div class="metric">
                    <span class="metric-label">Connection:</span>
                    <span class="metric-value" id="connection-metric">Unknown</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Lyrics Status:</span>
                    <span class="metric-value" id="lyrics-status-metric">Unknown</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Cache Hits:</span>
                    <span class="metric-value" id="cache-metric">0</span>
                </div>
            </div>
        </div>

        <!-- Advanced Settings Panel (dynamically created) -->
        <!-- This will be populated by JavaScript when user clicks "Advanced Settings" -->
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="text-center text-muted">
            Made with ❤️ by Sukarth Acharya<br>
            <a href="https://github.com/sukarth/liveytmusiclyrics" target="_blank">View on GitHub</a>
        </div>
    </div>

    <script src="popup.js"></script>
</body>

</html>