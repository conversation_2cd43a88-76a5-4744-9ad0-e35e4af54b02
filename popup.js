/**
 * Live YT Music Lyrics - Advanced Popup Controller
 *
 * Modern popup interface with comprehensive settings management,
 * real-time status monitoring, and seamless content script integration.
 *
 * <AUTHOR> Acharya
 * @version 2.0.0
 * @license MIT
 */

'use strict';

// ============================================================================
// CONFIGURATION - Matching content.js structure
// ============================================================================

/**
 * Configuration object for popup settings and defaults
 * @const {Object}
 */
const POPUP_CONFIG = Object.freeze({
    // Settings matching content.js CONTENT_CONFIG.SETTINGS
    SETTINGS: Object.freeze({
        STORAGE_KEYS: Object.freeze({
            GLOBAL: 'ytmusic_lyrics_global_settings',
            SONG_SPECIFIC: 'ytmusic_lyrics_song_settings',
            DYNAMIC_COLORS: 'ytmusic_lyrics_dynamic_colors'
        }),
        DEFAULTS: Object.freeze({
            SYNC_OFFSET: 0,           // seconds to offset lyrics sync
            AUTO_SCROLL: true,        // enable auto-scroll
            FONT_SIZE: 20,           // font size in px
            HIGHLIGHT_COLOR: '#ff0000', // active line highlight color
            DYNAMIC_THEME_COLORS: false // enable dynamic theme colors from album artwork
        }),
        LIMITS: Object.freeze({
            SYNC_OFFSET: { min: -10, max: 10, step: 0.1 },
            FONT_SIZE: { min: 10, max: 35, step: 1 }
        })
    }),

    // Connection and retry settings
    CONNECTION: Object.freeze({
        MAX_RETRIES: 3,
        RETRY_DELAY: 2000,
        MESSAGE_TIMEOUT: 5000,
        STATUS_UPDATE_INTERVAL: 1000
    }),

    // UI Text and messages
    UI_TEXT: Object.freeze({
        CONNECTING: 'Connecting...',
        CONNECTED: 'Connected',
        DISCONNECTED: 'Disconnected',
        NO_SONG: 'No song detected',
        OPEN_YOUTUBE_MUSIC: 'Open YouTube Music to start',
        SETTINGS_SAVED: 'Settings saved',
        SETTINGS_LOADED: 'Settings loaded',
        LYRICS_REFRESHED: 'Lyrics refreshed',
        ERROR_OCCURRED: 'An error occurred'
    }),

    // Error messages with recovery suggestions
    ERROR_MESSAGES: Object.freeze({
        CONNECTION_FAILED: {
            message: 'Failed to connect to YouTube Music',
            suggestion: 'Please refresh the YouTube Music page and try again'
        },
        NO_YOUTUBE_MUSIC: {
            message: 'YouTube Music not detected',
            suggestion: 'Please open YouTube Music in the active tab'
        },
        SETTINGS_SAVE_FAILED: {
            message: 'Failed to save settings',
            suggestion: 'Please check your browser storage permissions'
        },
        LYRICS_REFRESH_FAILED: {
            message: 'Failed to refresh lyrics',
            suggestion: 'Please check your internet connection and try again'
        },
        DYNAMIC_COLOR_FAILED: {
            message: 'Failed to extract dynamic colors',
            suggestion: 'Album artwork may not be available or accessible'
        },
        STORAGE_ACCESS_FAILED: {
            message: 'Failed to access browser storage',
            suggestion: 'Please check browser permissions and try again'
        },
        CONTENT_SCRIPT_NOT_LOADED: {
            message: 'Extension not loaded on this page',
            suggestion: 'Please refresh the YouTube Music page'
        }
    })
});

/**
 * Advanced popup controller with comprehensive feature set
 * @class
 */
class PopupController {
    /**
     * Initialize the popup controller with all advanced features
     */
    constructor() {
        // Core state
        this.currentSong = null;
        this.isConnected = false;
        this.retryCount = 0;
        this.connectionStatus = 'disconnected';

        // Settings system matching content.js
        this.currentSettings = { ...POPUP_CONFIG.SETTINGS.DEFAULTS };
        this.songSpecificSettings = new Map();
        this.dynamicColors = new Map();

        // UI state
        this.settingsPanelExpanded = false;
        this.statusUpdateInterval = null;

        // Performance monitoring
        this.lastStatusUpdate = 0;
        this.messageQueue = new Map();

        this.initializePopup();
    }

    /**
     * Initialize popup with comprehensive setup
     * @private
     */
    initializePopup() {
        try {
            this.setupErrorHandling();
            this.loadAllSettings();
            this.setupEventListeners();
            this.setupStatusMonitoring();
            this.checkCurrentSong();
            this.updateConnectionStatus('connecting');

            console.log('🎵 Advanced popup controller initialized');
        } catch (error) {
            console.error('❌ Popup initialization failed:', error);
            this.updateStatus('Initialization failed', 'error');
        }
    }

    /**
     * Set up comprehensive error handling
     * @private
     */
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('❌ Popup error:', event.error);
            this.updateStatus(POPUP_CONFIG.UI_TEXT.ERROR_OCCURRED, 'error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('❌ Unhandled promise rejection:', event.reason);
            this.updateStatus('Connection error', 'error');
            event.preventDefault();
        });
    }

    /**
     * Set up comprehensive real-time status monitoring
     * @private
     */
    setupStatusMonitoring() {
        // Update status every second
        this.statusUpdateInterval = setInterval(async () => {
            await this.performStatusCheck();
        }, POPUP_CONFIG.CONNECTION.STATUS_UPDATE_INTERVAL);

        // Initial status check
        setTimeout(() => this.performStatusCheck(), 500);
    }

    /**
     * Perform comprehensive status check
     * @private
     */
    async performStatusCheck() {
        try {
            const now = Date.now();

            // Throttle status updates to prevent excessive calls
            if (now - this.lastStatusUpdate < POPUP_CONFIG.CONNECTION.STATUS_UPDATE_INTERVAL) {
                return;
            }

            this.lastStatusUpdate = now;

            // Check if we're on YouTube Music
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];

            if (!activeTab?.url?.includes('music.youtube.com')) {
                this.updateConnectionStatus('disconnected');
                return;
            }

            // Try to ping content script
            try {
                const response = await this.sendMessageWithTimeout(
                    activeTab.id,
                    { action: 'ping' },
                    1000 // Short timeout for ping
                );

                if (response) {
                    this.updateConnectionStatus('connected');

                    // Get current song if we don't have one
                    if (!this.currentSong) {
                        this.checkCurrentSong();
                    }

                    // Get performance metrics if available
                    if (response.metrics) {
                        this.updatePerformanceMetrics(response.metrics);
                    }
                } else {
                    this.updateConnectionStatus('disconnected');
                }
            } catch (error) {
                this.updateConnectionStatus('disconnected');
            }

        } catch (error) {
            console.error('❌ Error in status check:', error);
            this.updateConnectionStatus('disconnected');
        }
    }

    /**
     * Update performance metrics display
     * @private
     */
    updatePerformanceMetrics(metrics) {
        try {
            // Update connection metric
            const connectionMetric = document.getElementById('connection-metric');
            if (connectionMetric) {
                connectionMetric.textContent = this.connectionStatus;
                connectionMetric.className = `metric-value ${this.connectionStatus}`;
            }

            // Update lyrics status
            const lyricsStatusMetric = document.getElementById('lyrics-status-metric');
            if (lyricsStatusMetric) {
                const status = this.currentSong ? 'Active' : 'Inactive';
                lyricsStatusMetric.textContent = status;
            }

            // Update cache metrics if available
            if (metrics && metrics.cacheHits !== undefined) {
                const cacheMetric = document.getElementById('cache-metric');
                if (cacheMetric) {
                    const hitRate = metrics.totalRequests > 0
                        ? Math.round((metrics.cacheHits / metrics.totalRequests) * 100)
                        : 0;
                    cacheMetric.textContent = `${hitRate}% (${metrics.cacheHits}/${metrics.totalRequests})`;
                }

                console.log('📊 Performance metrics updated:', {
                    cacheHits: metrics.cacheHits,
                    totalRequests: metrics.totalRequests,
                    avgResponseTime: metrics.avgResponseTime
                });
            }
        } catch (error) {
            console.error('❌ Error updating performance metrics:', error);
        }
    }

    /**
     * Toggle performance monitor visibility
     * @private
     */
    togglePerformanceMonitor() {
        const monitor = document.getElementById('performance-monitor');
        if (!monitor) return;

        if (monitor.classList.contains('hidden')) {
            monitor.classList.remove('hidden');
            this.updatePerformanceMetrics(); // Update with current data
        } else {
            monitor.classList.add('hidden');
        }
    }

    /**
     * Show performance monitor with current metrics
     * @private
     */
    showPerformanceMonitor() {
        const monitor = document.getElementById('performance-monitor');
        if (monitor) {
            monitor.classList.remove('hidden');
            this.updatePerformanceMetrics();
        }
    }

    /**
     * Load all settings from storage
     * @private
     */
    async loadAllSettings() {
        try {
            // Load global settings
            const globalSettings = await this.getStorageData(POPUP_CONFIG.SETTINGS.STORAGE_KEYS.GLOBAL);
            if (globalSettings) {
                this.currentSettings = { ...POPUP_CONFIG.SETTINGS.DEFAULTS, ...globalSettings };
            }

            // Load song-specific settings
            const songSettings = await this.getStorageData(POPUP_CONFIG.SETTINGS.STORAGE_KEYS.SONG_SPECIFIC);
            if (songSettings) {
                this.songSpecificSettings = new Map(Object.entries(songSettings));
            }

            // Load dynamic colors
            const dynamicColors = await this.getStorageData(POPUP_CONFIG.SETTINGS.STORAGE_KEYS.DYNAMIC_COLORS);
            if (dynamicColors) {
                this.dynamicColors = new Map(Object.entries(dynamicColors));
            }

            this.updateUI();
            console.log('🎵 Settings loaded successfully');
        } catch (error) {
            console.error('❌ Error loading settings:', error);
        }
    }

    /**
     * Set up comprehensive event listeners
     * @private
     */
    setupEventListeners() {
        try {
            // Basic toggle switches
            this.setupToggleListener('toggle-lyrics', 'toggleLyrics');
            this.setupToggleListener('toggle-autoscroll', 'updateSettings', 'AUTO_SCROLL');
            this.setupToggleListener('toggle-dynamic-theme', 'updateSettings', 'DYNAMIC_THEME_COLORS');

            // Action buttons
            document.getElementById('refresh-lyrics')?.addEventListener('click', () => {
                this.refreshLyrics();
            });

            document.getElementById('show-settings')?.addEventListener('click', () => {
                this.toggleSettingsPanel();
            });

            // Performance monitor toggle
            document.getElementById('toggle-monitor')?.addEventListener('click', () => {
                this.togglePerformanceMonitor();
            });

            // Song manager
            document.getElementById('show-song-manager')?.addEventListener('click', () => {
                this.toggleSongManager();
            });

            // Import/Export functionality
            document.getElementById('export-settings')?.addEventListener('click', () => {
                this.exportSettings();
            });

            document.getElementById('import-settings')?.addEventListener('click', () => {
                document.getElementById('import-file').click();
            });

            document.getElementById('import-file')?.addEventListener('change', (e) => {
                this.importSettings(e.target.files[0]);
            });

            // Advanced settings (will be added dynamically)
            this.setupAdvancedSettingsListeners();

            console.log('🎵 Event listeners set up successfully');
        } catch (error) {
            console.error('❌ Error setting up event listeners:', error);
        }
    }

    /**
     * Set up toggle listener with proper message handling
     * @private
     */
    setupToggleListener(elementId, action, settingKey = null) {
        const element = document.getElementById(elementId);
        if (!element) return;

        element.addEventListener('change', async (e) => {
            try {
                const enabled = e.target.checked;

                if (settingKey) {
                    // Update settings
                    this.currentSettings[settingKey] = enabled;
                    await this.saveSettings();

                    // Send to content script
                    await this.sendMessageToContent('updateSettings', {
                        [settingKey]: enabled
                    });
                } else {
                    // Direct action
                    await this.sendMessageToContent(action, { enabled });
                }

                this.updateStatus(`${elementId.replace('toggle-', '').replace('-', ' ')} ${enabled ? 'enabled' : 'disabled'}`, 'success');
            } catch (error) {
                console.error(`❌ Error handling ${elementId}:`, error);
                this.updateStatus('Failed to update setting', 'error');
                // Revert toggle state
                e.target.checked = !e.target.checked;
            }
        });
    }

    /**
     * Set up advanced settings listeners (for settings panel)
     * @private
     */
    setupAdvancedSettingsListeners() {
        // These will be set up when the settings panel is created
        // Placeholder for dynamic settings panel
    }

    /**
     * Update connection status indicator
     * @private
     */
    updateConnectionStatus(status = null) {
        const statusElement = document.getElementById('connection-status');
        if (!statusElement) return;

        if (status) {
            this.connectionStatus = status;
        }

        // Remove all status classes
        statusElement.classList.remove('connected', 'disconnected', 'connecting');

        // Add current status class
        statusElement.classList.add(this.connectionStatus);

        // Update tooltip/title
        const statusText = {
            'connected': POPUP_CONFIG.UI_TEXT.CONNECTED,
            'disconnected': POPUP_CONFIG.UI_TEXT.DISCONNECTED,
            'connecting': POPUP_CONFIG.UI_TEXT.CONNECTING
        };

        statusElement.title = statusText[this.connectionStatus] || 'Unknown';
    }

    /**
     * Toggle settings panel visibility
     * @private
     */
    toggleSettingsPanel() {
        this.settingsPanelExpanded = !this.settingsPanelExpanded;

        if (this.settingsPanelExpanded) {
            this.createSettingsPanel();
        } else {
            this.hideSettingsPanel();
        }
    }

    /**
     * Create and show advanced settings panel
     * @private
     */
    createSettingsPanel() {
        // Remove existing panel
        this.hideSettingsPanel();

        const content = document.querySelector('.content');
        const settingsPanel = document.createElement('div');
        settingsPanel.className = 'settings-panel expanded';
        settingsPanel.id = 'settings-panel';

        settingsPanel.innerHTML = `
            <div class="settings-header">
                <span>Advanced Settings</span>
                <span>▼</span>
            </div>
            <div class="settings-content">
                ${this.generateSettingsHTML()}
            </div>
        `;

        content.appendChild(settingsPanel);
        this.setupSettingsPanelListeners();

        // Update button text
        const button = document.getElementById('show-settings');
        if (button) button.textContent = '▲ Hide Settings';
    }

    /**
     * Generate HTML for advanced settings panel
     * @private
     */
    generateSettingsHTML() {
        return `
            <div class="setting-group">
                <label class="setting-label">Sync Offset (seconds)</label>
                <input type="range"
                       class="range-input"
                       id="sync-offset-range"
                       min="${POPUP_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.min}"
                       max="${POPUP_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.max}"
                       step="${POPUP_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.step}"
                       value="${this.currentSettings.SYNC_OFFSET}">
                <input type="number"
                       class="setting-input"
                       id="sync-offset-input"
                       min="${POPUP_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.min}"
                       max="${POPUP_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.max}"
                       step="${POPUP_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.step}"
                       value="${this.currentSettings.SYNC_OFFSET}">
            </div>

            <div class="setting-group">
                <label class="setting-label">Font Size (px)</label>
                <input type="range"
                       class="range-input"
                       id="font-size-range"
                       min="${POPUP_CONFIG.SETTINGS.LIMITS.FONT_SIZE.min}"
                       max="${POPUP_CONFIG.SETTINGS.LIMITS.FONT_SIZE.max}"
                       step="${POPUP_CONFIG.SETTINGS.LIMITS.FONT_SIZE.step}"
                       value="${this.currentSettings.FONT_SIZE}">
                <input type="number"
                       class="setting-input"
                       id="font-size-input"
                       min="${POPUP_CONFIG.SETTINGS.LIMITS.FONT_SIZE.min}"
                       max="${POPUP_CONFIG.SETTINGS.LIMITS.FONT_SIZE.max}"
                       step="${POPUP_CONFIG.SETTINGS.LIMITS.FONT_SIZE.step}"
                       value="${this.currentSettings.FONT_SIZE}">
            </div>

            <div class="setting-group" id="manual-color-group">
                <label class="setting-label">Theme Color</label>
                <div class="color-input-group">
                    <div class="color-preview"
                         id="color-preview"
                         style="background-color: ${this.currentSettings.HIGHLIGHT_COLOR}"></div>
                    <input type="text"
                           class="setting-input color-input"
                           id="color-input"
                           value="${this.currentSettings.HIGHLIGHT_COLOR}"
                           pattern="^#[0-9A-Fa-f]{6}$">
                </div>
            </div>

            <div class="setting-group">
                <button class="btn btn-secondary" id="save-current-song">Save for Current Song</button>
                <button class="btn btn-secondary" id="save-all-songs">Save for All Songs</button>
                <button class="btn btn-secondary" id="reset-defaults">Reset to Defaults</button>
            </div>
        `;
    }

    /**
     * Hide settings panel
     * @private
     */
    hideSettingsPanel() {
        const panel = document.getElementById('settings-panel');
        if (panel) {
            panel.remove();
        }

        this.settingsPanelExpanded = false;

        // Update button text
        const button = document.getElementById('show-settings');
        if (button) button.textContent = '⚙️ Advanced Settings';
    }

    /**
     * Check current song with enhanced error handling
     * @private
     */
    async checkCurrentSong() {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];

            if (!activeTab?.url) {
                this.updateConnectionStatus('disconnected');
                this.updateStatus('No active tab found', 'error');
                return;
            }

            if (!activeTab.url.includes('music.youtube.com')) {
                this.updateConnectionStatus('disconnected');
                this.updateStatus('Please open YouTube Music', 'info');
                this.updateCurrentSong(null);
                return;
            }

            this.updateConnectionStatus('connecting');

            // Attempt to connect to content script
            const response = await this.sendMessageWithTimeout(
                activeTab.id,
                { action: 'getCurrentSong' },
                POPUP_CONFIG.CONNECTION.MESSAGE_TIMEOUT
            );

            if (response && response.status !== 'no_song') {
                this.updateCurrentSong(response);
                this.updateConnectionStatus('connected');
                this.retryCount = 0;
                this.updateStatus('Connected to YouTube Music', 'success');
            } else {
                this.handleConnectionFailure();
            }

        } catch (error) {
            console.error('❌ Error checking current song:', error);
            this.handleConnectionFailure();
        }
    }

    /**
     * Set up settings panel event listeners
     * @private
     */
    setupSettingsPanelListeners() {
        // Sync offset controls
        this.setupRangeInputSync('sync-offset', 'SYNC_OFFSET');

        // Font size controls
        this.setupRangeInputSync('font-size', 'FONT_SIZE');

        // Color input
        this.setupColorInput();

        // Action buttons
        document.getElementById('save-current-song')?.addEventListener('click', () => {
            this.saveSettingsForCurrentSong();
        });

        document.getElementById('save-all-songs')?.addEventListener('click', () => {
            this.saveSettingsGlobally();
        });

        document.getElementById('reset-defaults')?.addEventListener('click', () => {
            this.resetToDefaults();
        });

        // Settings header click to collapse
        document.querySelector('.settings-header')?.addEventListener('click', () => {
            this.toggleSettingsPanel();
        });
    }

    /**
     * Set up synchronized range and number inputs
     * @private
     */
    setupRangeInputSync(baseName, settingKey) {
        const rangeInput = document.getElementById(`${baseName}-range`);
        const numberInput = document.getElementById(`${baseName}-input`);

        if (!rangeInput || !numberInput) return;

        const updateSetting = async (value) => {
            this.currentSettings[settingKey] = parseFloat(value);
            await this.sendMessageToContent('updateSettings', {
                [settingKey]: this.currentSettings[settingKey]
            });
        };

        rangeInput.addEventListener('input', (e) => {
            numberInput.value = e.target.value;
            updateSetting(e.target.value);
        });

        numberInput.addEventListener('input', (e) => {
            rangeInput.value = e.target.value;
            updateSetting(e.target.value);
        });
    }

    /**
     * Set up color input with preview
     * @private
     */
    setupColorInput() {
        const colorInput = document.getElementById('color-input');
        const colorPreview = document.getElementById('color-preview');

        if (!colorInput || !colorPreview) return;

        colorInput.addEventListener('input', async (e) => {
            const color = e.target.value;
            if (/^#[0-9A-Fa-f]{6}$/.test(color)) {
                colorPreview.style.backgroundColor = color;
                this.currentSettings.HIGHLIGHT_COLOR = color;

                await this.sendMessageToContent('updateSettings', {
                    HIGHLIGHT_COLOR: color
                });
            }
        });

        colorPreview.addEventListener('click', () => {
            // Create temporary color picker
            const tempInput = document.createElement('input');
            tempInput.type = 'color';
            tempInput.value = this.currentSettings.HIGHLIGHT_COLOR;
            tempInput.style.position = 'absolute';
            tempInput.style.left = '-9999px';

            document.body.appendChild(tempInput);
            tempInput.click();

            tempInput.addEventListener('change', (e) => {
                const color = e.target.value;
                colorInput.value = color;
                colorPreview.style.backgroundColor = color;
                this.currentSettings.HIGHLIGHT_COLOR = color;

                this.sendMessageToContent('updateSettings', {
                    HIGHLIGHT_COLOR: color
                });

                document.body.removeChild(tempInput);
            });
        });
    }

    /**
     * Send message with timeout and retry logic
     * @param {number} tabId - Tab ID
     * @param {Object} message - Message to send
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise} Response or timeout
     * @private
     */
    sendMessageWithTimeout(tabId, message, timeout = POPUP_CONFIG.CONNECTION.MESSAGE_TIMEOUT) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('Message timeout'));
            }, timeout);

            chrome.tabs.sendMessage(tabId, message, (response) => {
                clearTimeout(timeoutId);
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * Handle connection failures with enhanced retry logic
     * @private
     */
    handleConnectionFailure() {
        this.isConnected = false;
        this.updateConnectionStatus('disconnected');
        this.retryCount++;

        if (this.retryCount <= POPUP_CONFIG.CONNECTION.MAX_RETRIES) {
            this.updateStatus(`${POPUP_CONFIG.UI_TEXT.CONNECTING} (${this.retryCount}/${POPUP_CONFIG.CONNECTION.MAX_RETRIES})`, 'warning');
            setTimeout(() => this.checkCurrentSong(), POPUP_CONFIG.CONNECTION.RETRY_DELAY * this.retryCount);
        } else {
            this.updateStatus('Connection failed. Please refresh YouTube Music.', 'error');
            this.updateCurrentSong(null);
        }
    }

    /**
     * Update current song display with enhanced information
     * @private
     */
    updateCurrentSong(songInfo) {
        const titleElement = document.getElementById('song-title');
        const artistElement = document.getElementById('song-artist');
        const durationElement = document.getElementById('song-duration');
        const albumArtElement = document.getElementById('album-art');
        const themePreview = document.getElementById('dynamic-theme-preview');

        if (songInfo && songInfo.title && songInfo.artist) {
            titleElement.textContent = songInfo.title;
            artistElement.textContent = songInfo.artist;

            // Show duration if available
            if (songInfo.duration) {
                const minutes = Math.floor(songInfo.duration / 60);
                const seconds = songInfo.duration % 60;
                durationElement.textContent = `${minutes}:${String(seconds).padStart(2, '0')}`;
                durationElement.classList.remove('hidden');
            } else {
                durationElement.classList.add('hidden');
            }

            // Update album art placeholder
            albumArtElement.textContent = '🎵';

            // Show dynamic theme preview if enabled
            if (this.currentSettings.DYNAMIC_THEME_COLORS) {
                themePreview.classList.remove('hidden');
                // Update preview color if available
                const songKey = `${songInfo.title}-${songInfo.artist}`;
                const dynamicColor = this.dynamicColors.get(songKey);
                if (dynamicColor) {
                    themePreview.style.backgroundColor = dynamicColor.color;
                }
            } else {
                themePreview.classList.add('hidden');
            }

            this.currentSong = songInfo;

            // Load song-specific settings if available
            this.loadSongSpecificSettings(songInfo);

        } else {
            titleElement.textContent = POPUP_CONFIG.UI_TEXT.NO_SONG;
            artistElement.textContent = POPUP_CONFIG.UI_TEXT.OPEN_YOUTUBE_MUSIC;
            durationElement.classList.add('hidden');
            themePreview.classList.add('hidden');
            albumArtElement.textContent = '🎵';
            this.currentSong = null;
        }
    }

    /**
     * Load song-specific settings if available
     * @private
     */
    async loadSongSpecificSettings(songInfo) {
        if (!songInfo) return;

        const songKey = `${songInfo.title}-${songInfo.artist}`;
        const songSettings = this.songSpecificSettings.get(songKey);

        if (songSettings) {
            this.currentSettings = { ...this.currentSettings, ...songSettings };
            this.updateUI();
            this.updateStatus(POPUP_CONFIG.UI_TEXT.SETTINGS_LOADED, 'info');

            // Send updated settings to content script
            await this.sendMessageToContent('updateSettings', this.currentSettings);
        }

        // Load dynamic theme color if available and enabled
        if (this.currentSettings.DYNAMIC_THEME_COLORS) {
            await this.loadDynamicThemeColor(songInfo);
        }
    }

    /**
     * Load and apply dynamic theme color for current song
     * @private
     */
    async loadDynamicThemeColor(songInfo) {
        if (!songInfo) return;

        try {
            const songKey = `${songInfo.title}-${songInfo.artist}`;
            const dynamicColor = this.dynamicColors.get(songKey);

            if (dynamicColor && dynamicColor.color) {
                // Update theme preview
                const themePreview = document.getElementById('dynamic-theme-preview');
                if (themePreview) {
                    themePreview.style.backgroundColor = dynamicColor.color;
                    themePreview.classList.remove('hidden');
                }

                // Apply theme color to popup
                this.applyDynamicThemeColor(dynamicColor.color);

                console.log('🎨 Applied dynamic theme color:', dynamicColor.color);
            } else {
                // Request dynamic color extraction from content script
                await this.requestDynamicColorExtraction(songInfo);
            }
        } catch (error) {
            console.error('❌ Error loading dynamic theme color:', error);
        }
    }

    /**
     * Request dynamic color extraction from content script
     * @private
     */
    async requestDynamicColorExtraction(songInfo) {
        try {
            const response = await this.sendMessageToContent('extractDynamicColor', {
                song: songInfo
            });

            if (response && response.color) {
                const songKey = `${songInfo.title}-${songInfo.artist}`;

                // Cache the color
                this.dynamicColors.set(songKey, {
                    color: response.color,
                    timestamp: Date.now()
                });

                // Save to storage
                await this.saveStorageData(
                    POPUP_CONFIG.SETTINGS.STORAGE_KEYS.DYNAMIC_COLORS,
                    Object.fromEntries(this.dynamicColors)
                );

                // Apply the color
                this.applyDynamicThemeColor(response.color);

                // Update preview
                const themePreview = document.getElementById('dynamic-theme-preview');
                if (themePreview) {
                    themePreview.style.backgroundColor = response.color;
                    themePreview.classList.remove('hidden');
                }

                console.log('🎨 Extracted and applied dynamic color:', response.color);
            }
        } catch (error) {
            console.error('❌ Error requesting dynamic color extraction:', error);
        }
    }

    /**
     * Apply dynamic theme color to popup interface
     * @private
     */
    applyDynamicThemeColor(color) {
        try {
            // Parse the hex color to RGB
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);

            // Update CSS custom properties
            const root = document.documentElement;
            root.style.setProperty('--primary-color', color);
            root.style.setProperty('--primary-light', this.lightenColor(color, 0.3));
            root.style.setProperty('--primary-dark', this.darkenColor(color, 0.3));
            root.style.setProperty('--primary-gradient', `linear-gradient(90deg, ${color} 0%, ${this.lightenColor(color, 0.2)} 100%)`);

            // Update alpha variants
            root.style.setProperty('--primary-alpha-10', `rgba(${r}, ${g}, ${b}, 0.1)`);
            root.style.setProperty('--primary-alpha-15', `rgba(${r}, ${g}, ${b}, 0.15)`);
            root.style.setProperty('--primary-alpha-20', `rgba(${r}, ${g}, ${b}, 0.2)`);
            root.style.setProperty('--primary-alpha-30', `rgba(${r}, ${g}, ${b}, 0.3)`);
            root.style.setProperty('--primary-alpha-40', `rgba(${r}, ${g}, ${b}, 0.4)`);
            root.style.setProperty('--primary-alpha-50', `rgba(${r}, ${g}, ${b}, 0.5)`);

        } catch (error) {
            console.error('❌ Error applying dynamic theme color:', error);
        }
    }

    /**
     * Lighten a hex color by a percentage
     * @private
     */
    lightenColor(color, percent) {
        const hex = color.replace('#', '');
        const r = Math.min(255, Math.floor(parseInt(hex.substr(0, 2), 16) + (255 - parseInt(hex.substr(0, 2), 16)) * percent));
        const g = Math.min(255, Math.floor(parseInt(hex.substr(2, 2), 16) + (255 - parseInt(hex.substr(2, 2), 16)) * percent));
        const b = Math.min(255, Math.floor(parseInt(hex.substr(4, 2), 16) + (255 - parseInt(hex.substr(4, 2), 16)) * percent));

        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }

    /**
     * Darken a hex color by a percentage
     * @private
     */
    darkenColor(color, percent) {
        const hex = color.replace('#', '');
        const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - percent));
        const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - percent));
        const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - percent));

        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }

    updateCurrentSong(songInfo) {
        const titleElement = document.getElementById('song-title');
        const artistElement = document.getElementById('song-artist');

        if (songInfo) {
            titleElement.textContent = songInfo.title;
            artistElement.textContent = songInfo.artist;
            this.currentSong = songInfo;
            this.updateStatus('Song detected successfully', 'success');
        } else {
            titleElement.textContent = 'No song detected';
            artistElement.textContent = 'Open YouTube Music to start';
            this.currentSong = null;
        }
    }

    /**
     * Refresh lyrics with enhanced error handling
     * @private
     */
    async refreshLyrics() {
        if (!this.currentSong) {
            this.updateStatus('No song to refresh lyrics for', 'error');
            return;
        }

        this.showLoading(true);
        this.updateStatus('Fetching fresh lyrics...', 'info');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            const response = await this.sendMessageWithTimeout(tab.id, {
                action: 'refreshLyrics',
                song: this.currentSong
            });

            this.showLoading(false);

            if (response && response.success) {
                this.updateStatus(POPUP_CONFIG.UI_TEXT.LYRICS_REFRESHED, 'success');
            } else {
                this.updateStatus('Failed to refresh lyrics', 'error');
            }
        } catch (error) {
            this.showLoading(false);
            this.updateStatus('Error refreshing lyrics', 'error');
            console.error('❌ Refresh error:', error);
        }
    }

    /**
     * Save settings for current song only
     * @private
     */
    async saveSettingsForCurrentSong() {
        if (!this.currentSong) {
            this.updateStatus('No current song to save settings for', 'error');
            return;
        }

        try {
            const songKey = `${this.currentSong.title}-${this.currentSong.artist}`;
            this.songSpecificSettings.set(songKey, { ...this.currentSettings });

            await this.saveStorageData(
                POPUP_CONFIG.SETTINGS.STORAGE_KEYS.SONG_SPECIFIC,
                Object.fromEntries(this.songSpecificSettings)
            );

            this.updateStatus('Settings saved for current song', 'success');
        } catch (error) {
            console.error('❌ Error saving song settings:', error);
            this.updateStatus('Failed to save settings', 'error');
        }
    }

    /**
     * Save settings globally for all songs
     * @private
     */
    async saveSettingsGlobally() {
        try {
            await this.saveStorageData(
                POPUP_CONFIG.SETTINGS.STORAGE_KEYS.GLOBAL,
                this.currentSettings
            );

            this.updateStatus(POPUP_CONFIG.UI_TEXT.SETTINGS_SAVED, 'success');
        } catch (error) {
            console.error('❌ Error saving global settings:', error);
            this.updateStatus('Failed to save settings', 'error');
        }
    }

    /**
     * Reset settings to defaults
     * @private
     */
    async resetToDefaults() {
        try {
            this.currentSettings = { ...POPUP_CONFIG.SETTINGS.DEFAULTS };
            this.updateUI();

            await this.sendMessageToContent('updateSettings', this.currentSettings);
            this.updateStatus('Settings reset to defaults', 'success');
        } catch (error) {
            console.error('❌ Error resetting settings:', error);
            this.updateStatus('Failed to reset settings', 'error');
        }
    }

    /**
     * Show/hide loading state for buttons
     * @private
     */
    showLoading(show) {
        const refreshText = document.getElementById('refresh-text');
        const refreshLoading = document.getElementById('refresh-loading');
        const refreshButton = document.getElementById('refresh-lyrics');

        if (show) {
            refreshText?.classList.add('hidden');
            refreshLoading?.classList.remove('hidden');
            if (refreshButton) refreshButton.disabled = true;
        } else {
            refreshText?.classList.remove('hidden');
            refreshLoading?.classList.add('hidden');
            if (refreshButton) refreshButton.disabled = false;
        }
    }

    /**
     * Update status message with enhanced styling and error handling
     * @private
     */
    updateStatus(message, type = 'info', errorKey = null) {
        const statusElement = document.getElementById('status');
        if (!statusElement) return;

        // Handle error messages with suggestions
        if (type === 'error' && errorKey && POPUP_CONFIG.ERROR_MESSAGES[errorKey]) {
            const errorInfo = POPUP_CONFIG.ERROR_MESSAGES[errorKey];
            this.showErrorWithSuggestion(errorInfo.message, errorInfo.suggestion);
            return;
        }

        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
        statusElement.classList.remove('hidden');

        // Auto-hide after delay based on type
        const hideDelay = {
            'success': 3000,
            'info': 5000,
            'warning': 7000,
            'error': 10000
        };

        setTimeout(() => {
            if (statusElement.textContent === message) {
                statusElement.classList.add('hidden');
            }
        }, hideDelay[type] || 5000);
    }

    /**
     * Show error message with recovery suggestion
     * @private
     */
    showErrorWithSuggestion(message, suggestion) {
        const statusElement = document.getElementById('status');
        if (!statusElement) return;

        statusElement.innerHTML = `
            <div class="error-message">
                <div class="error-title">${message}</div>
                <div class="error-suggestion">${suggestion}</div>
            </div>
        `;
        statusElement.className = 'status error';
        statusElement.classList.remove('hidden');

        // Auto-hide after 15 seconds for errors with suggestions
        setTimeout(() => {
            statusElement.classList.add('hidden');
        }, 15000);
    }

    /**
     * Show toast notification for quick feedback
     * @private
     */
    showToast(message, type = 'info', duration = 3000) {
        // Remove existing toast
        const existingToast = document.querySelector('.toast-notification');
        if (existingToast) {
            existingToast.remove();
        }

        // Create new toast
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.textContent = message;

        // Add to body
        document.body.appendChild(toast);

        // Show with animation
        setTimeout(() => toast.classList.add('show'), 10);

        // Auto-hide
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, duration);
    }

    /**
     * Handle specific error types with appropriate user feedback
     * @private
     */
    handleError(error, context = 'general') {
        console.error(`❌ Error in ${context}:`, error);

        const errorMessage = error.message || error.toString();

        // Categorize errors and provide appropriate feedback
        if (errorMessage.includes('Extension context invalidated')) {
            this.updateStatus('', 'error', 'CONTENT_SCRIPT_NOT_LOADED');
        } else if (errorMessage.includes('No tab with id')) {
            this.updateStatus('', 'error', 'NO_YOUTUBE_MUSIC');
        } else if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
            this.updateStatus('', 'error', 'CONNECTION_FAILED');
        } else if (errorMessage.includes('storage')) {
            this.updateStatus('', 'error', 'STORAGE_ACCESS_FAILED');
        } else {
            // Generic error
            this.updateStatus(`${context}: ${errorMessage}`, 'error');
        }
    }

    /**
     * Send message to content script with error handling
     * @private
     */
    async sendMessageToContent(action, data = {}) {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];

            if (!activeTab?.url?.includes('music.youtube.com')) {
                this.updateStatus('Please open YouTube Music first', 'error');
                return null;
            }

            const response = await this.sendMessageWithTimeout(
                activeTab.id,
                { action, ...data },
                POPUP_CONFIG.CONNECTION.MESSAGE_TIMEOUT
            );

            return response;

        } catch (error) {
            console.error('❌ Error sending message to content script:', error);
            this.updateStatus('Communication error with page', 'error');
            this.updateConnectionStatus('disconnected');
            return null;
        }
    }

    /**
     * Update UI elements with current settings
     * @private
     */
    updateUI() {
        try {
            // Update toggle switches
            const lyricsToggle = document.getElementById('toggle-lyrics');
            const autoscrollToggle = document.getElementById('toggle-autoscroll');
            const dynamicThemeToggle = document.getElementById('toggle-dynamic-theme');

            if (lyricsToggle) lyricsToggle.checked = true; // Always enabled in new version
            if (autoscrollToggle) autoscrollToggle.checked = this.currentSettings.AUTO_SCROLL;
            if (dynamicThemeToggle) dynamicThemeToggle.checked = this.currentSettings.DYNAMIC_THEME_COLORS;

            // Update settings panel if open
            this.updateSettingsPanel();

        } catch (error) {
            console.error('❌ Error updating UI:', error);
        }
    }

    /**
     * Update settings panel inputs
     * @private
     */
    updateSettingsPanel() {
        if (!this.settingsPanelExpanded) return;

        // Update range and number inputs
        const syncOffsetRange = document.getElementById('sync-offset-range');
        const syncOffsetInput = document.getElementById('sync-offset-input');
        const fontSizeRange = document.getElementById('font-size-range');
        const fontSizeInput = document.getElementById('font-size-input');
        const colorInput = document.getElementById('color-input');
        const colorPreview = document.getElementById('color-preview');

        if (syncOffsetRange) syncOffsetRange.value = this.currentSettings.SYNC_OFFSET;
        if (syncOffsetInput) syncOffsetInput.value = this.currentSettings.SYNC_OFFSET;
        if (fontSizeRange) fontSizeRange.value = this.currentSettings.FONT_SIZE;
        if (fontSizeInput) fontSizeInput.value = this.currentSettings.FONT_SIZE;
        if (colorInput) colorInput.value = this.currentSettings.HIGHLIGHT_COLOR;
        if (colorPreview) colorPreview.style.backgroundColor = this.currentSettings.HIGHLIGHT_COLOR;

        // Update manual color group visibility
        const manualColorGroup = document.getElementById('manual-color-group');
        if (manualColorGroup) {
            if (this.currentSettings.DYNAMIC_THEME_COLORS) {
                manualColorGroup.style.opacity = '0.5';
                manualColorGroup.style.pointerEvents = 'none';
            } else {
                manualColorGroup.style.opacity = '1';
                manualColorGroup.style.pointerEvents = 'auto';
            }
        }
    }

    /**
     * Storage utility methods
     * @private
     */
    async getStorageData(key) {
        return new Promise((resolve) => {
            chrome.storage.sync.get([key], (result) => {
                if (chrome.runtime.lastError) {
                    console.error('❌ Storage get error:', chrome.runtime.lastError);
                    resolve(null);
                } else {
                    resolve(result[key]);
                }
            });
        });
    }

    async saveStorageData(key, data) {
        return new Promise((resolve, reject) => {
            chrome.storage.sync.set({ [key]: data }, () => {
                if (chrome.runtime.lastError) {
                    console.error('❌ Storage save error:', chrome.runtime.lastError);
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });
    }

    /**
     * Toggle song manager panel
     * @private
     */
    toggleSongManager() {
        const existingPanel = document.getElementById('song-manager-panel');

        if (existingPanel) {
            this.hideSongManager();
        } else {
            this.showSongManager();
        }
    }

    /**
     * Show song manager panel
     * @private
     */
    showSongManager() {
        this.hideSongManager(); // Remove existing panel

        const content = document.querySelector('.content');
        const panel = document.createElement('div');
        panel.className = 'song-manager-panel';
        panel.id = 'song-manager-panel';

        panel.innerHTML = `
            <div class="song-manager-header">
                <span>📋 Song Settings Manager</span>
                <button class="btn-icon" onclick="popupController.hideSongManager()">×</button>
            </div>
            <div class="song-manager-content" id="song-manager-content">
                ${this.generateSongManagerHTML()}
            </div>
        `;

        content.appendChild(panel);

        // Update button text
        const button = document.getElementById('show-song-manager');
        if (button) button.textContent = '▲ Hide Song Manager';
    }

    /**
     * Hide song manager panel
     * @private
     */
    hideSongManager() {
        const panel = document.getElementById('song-manager-panel');
        if (panel) {
            panel.remove();
        }

        // Update button text
        const button = document.getElementById('show-song-manager');
        if (button) button.textContent = '📋 Manage Song Settings';
    }

    /**
     * Generate HTML for song manager content
     * @private
     */
    generateSongManagerHTML() {
        if (this.songSpecificSettings.size === 0) {
            return `
                <div class="empty-state">
                    <div>No song-specific settings found</div>
                    <div>Play songs and save custom settings to see them here</div>
                </div>
            `;
        }

        let html = '';
        for (const [songKey, settings] of this.songSpecificSettings.entries()) {
            const [title, artist] = songKey.split('-');
            html += `
                <div class="song-item">
                    <div class="song-details-mini">
                        <div class="song-title-mini">${title}</div>
                        <div class="song-artist-mini">${artist}</div>
                    </div>
                    <div class="song-actions">
                        <button class="btn-mini" onclick="popupController.editSongSettings('${songKey}')">Edit</button>
                        <button class="btn-mini danger" onclick="popupController.deleteSongSettings('${songKey}')">Delete</button>
                    </div>
                </div>
            `;
        }

        return html;
    }

    /**
     * Edit settings for a specific song
     * @private
     */
    async editSongSettings(songKey) {
        const settings = this.songSpecificSettings.get(songKey);
        if (!settings) return;

        // Load the settings temporarily
        const originalSettings = { ...this.currentSettings };
        this.currentSettings = { ...this.currentSettings, ...settings };
        this.updateUI();

        this.showToast(`Loaded settings for ${songKey.split('-')[0]}`, 'info');

        // Show settings panel if not already open
        if (!this.settingsPanelExpanded) {
            this.toggleSettingsPanel();
        }
    }

    /**
     * Delete settings for a specific song
     * @private
     */
    async deleteSongSettings(songKey) {
        if (!confirm(`Delete settings for "${songKey.split('-')[0]}"?`)) {
            return;
        }

        try {
            this.songSpecificSettings.delete(songKey);

            await this.saveStorageData(
                POPUP_CONFIG.SETTINGS.STORAGE_KEYS.SONG_SPECIFIC,
                Object.fromEntries(this.songSpecificSettings)
            );

            // Refresh the song manager display
            if (document.getElementById('song-manager-panel')) {
                this.showSongManager();
            }

            this.showToast('Song settings deleted', 'success');
        } catch (error) {
            console.error('❌ Error deleting song settings:', error);
            this.showToast('Failed to delete settings', 'error');
        }
    }

    /**
     * Export all settings to JSON file
     * @private
     */
    async exportSettings() {
        try {
            const exportData = {
                version: '2.0.0',
                timestamp: new Date().toISOString(),
                globalSettings: this.currentSettings,
                songSpecificSettings: Object.fromEntries(this.songSpecificSettings),
                dynamicColors: Object.fromEntries(this.dynamicColors)
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ytmusic-lyrics-settings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showToast('Settings exported successfully', 'success');
        } catch (error) {
            console.error('❌ Error exporting settings:', error);
            this.showToast('Failed to export settings', 'error');
        }
    }

    /**
     * Import settings from JSON file
     * @private
     */
    async importSettings(file) {
        if (!file) return;

        try {
            const text = await file.text();
            const importData = JSON.parse(text);

            // Validate import data
            if (!importData.version || !importData.globalSettings) {
                throw new Error('Invalid settings file format');
            }

            // Import global settings
            if (importData.globalSettings) {
                this.currentSettings = { ...POPUP_CONFIG.SETTINGS.DEFAULTS, ...importData.globalSettings };
                await this.saveStorageData(POPUP_CONFIG.SETTINGS.STORAGE_KEYS.GLOBAL, this.currentSettings);
            }

            // Import song-specific settings
            if (importData.songSpecificSettings) {
                this.songSpecificSettings = new Map(Object.entries(importData.songSpecificSettings));
                await this.saveStorageData(
                    POPUP_CONFIG.SETTINGS.STORAGE_KEYS.SONG_SPECIFIC,
                    importData.songSpecificSettings
                );
            }

            // Import dynamic colors
            if (importData.dynamicColors) {
                this.dynamicColors = new Map(Object.entries(importData.dynamicColors));
                await this.saveStorageData(
                    POPUP_CONFIG.SETTINGS.STORAGE_KEYS.DYNAMIC_COLORS,
                    importData.dynamicColors
                );
            }

            // Update UI
            this.updateUI();

            // Refresh song manager if open
            if (document.getElementById('song-manager-panel')) {
                this.showSongManager();
            }

            this.showToast(`Settings imported successfully (${Object.keys(importData.songSpecificSettings || {}).length} songs)`, 'success');

        } catch (error) {
            console.error('❌ Error importing settings:', error);
            this.showToast('Failed to import settings: ' + error.message, 'error');
        }

        // Clear the file input
        document.getElementById('import-file').value = '';
    }

    /**
     * Cleanup method called when popup is closed
     * @private
     */
    cleanup() {
        if (this.statusUpdateInterval) {
            clearInterval(this.statusUpdateInterval);
        }
    }
}
}

// ============================================================================
// INITIALIZATION AND LIFECYCLE MANAGEMENT
// ============================================================================

/**
 * Global popup controller instance
 */
let popupController = null;

/**
 * Initialize popup with comprehensive error handling
 */
function initializePopup() {
    try {
        popupController = new PopupController();
        console.log('🎵 Advanced Live YT Music Lyrics popup initialized');

        // Make available for debugging
        if (typeof window !== 'undefined') {
            window.popupController = popupController;
        }

        // Set up cleanup on window unload
        window.addEventListener('beforeunload', () => {
            if (popupController) {
                popupController.cleanup();
            }
        });

    } catch (error) {
        console.error('❌ Failed to initialize popup:', error);

        // Show fallback error message
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = 'Failed to initialize popup';
            statusElement.className = 'status error';
            statusElement.classList.remove('hidden');
        }
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePopup);
} else {
    initializePopup();
}

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PopupController, POPUP_CONFIG };
}
