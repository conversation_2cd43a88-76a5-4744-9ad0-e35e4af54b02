/**
 * Live YT Music Lyrics - Popup Controller
 *
 * Production-ready popup interface with advanced state management,
 * error handling, and cross-browser compatibility.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @license MIT
 */

'use strict';

class PopupController {
    constructor() {
        this.currentSong = null;
        this.isConnected = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        this.settings = {
            lyricsEnabled: true,
            autoScroll: true,
            showOverlay: true
        };

        this.initializePopup();
    }

    /**
     * Initialize popup with error boundaries
     * @private
     */
    initializePopup() {
        try {
            this.setupErrorHandling();
            this.init();
        } catch (error) {
            console.error('Popup initialization failed:', error);
            this.updateStatus('Initialization failed', 'error');
        }
    }

    /**
     * Set up global error handling for the popup
     * @private
     */
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Popup error:', event.error);
            this.updateStatus('An error occurred', 'error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.updateStatus('Connection error', 'error');
        });
    }

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.checkCurrentSong();
        this.updateStatus('Ready to fetch lyrics', 'info');
    }

    setupEventListeners() {
        // Toggle switches
        document.getElementById('toggle-lyrics').addEventListener('change', (e) => {
            this.settings.lyricsEnabled = e.target.checked;
            this.saveSettings();
            this.sendMessageToContent('toggleLyrics', { enabled: e.target.checked });
        });

        document.getElementById('toggle-autoscroll').addEventListener('change', (e) => {
            this.settings.autoScroll = e.target.checked;
            this.saveSettings();
            this.sendMessageToContent('toggleAutoScroll', { enabled: e.target.checked });
        });

        document.getElementById('toggle-overlay').addEventListener('change', (e) => {
            this.settings.showOverlay = e.target.checked;
            this.saveSettings();
            this.sendMessageToContent('toggleOverlay', { enabled: e.target.checked });
        });

        // Refresh button
        document.getElementById('refresh-lyrics').addEventListener('click', () => {
            this.refreshLyrics();
        });

        // GitHub link with error handling
        document.getElementById('github-link').addEventListener('click', (e) => {
            e.preventDefault();
            this.openGitHubPage();
        });
    }

    /**
     * Open GitHub repository page
     * @private
     */
    openGitHubPage() {
        try {
            chrome.tabs.create({
                url: 'https://github.com/sukarth/liveytmusiclyrics'
            });
        } catch (error) {
            console.error('Failed to open GitHub page:', error);
            // Fallback: copy URL to clipboard or show message
            this.updateStatus('Visit: github.com/sukarth/liveytmusiclyrics', 'info');
        }
    }

    /**
     * Check current song with intelligent retry mechanism
     * @private
     */
    async checkCurrentSong() {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];

            if (!activeTab?.url) {
                this.updateStatus('No active tab found', 'error');
                return;
            }

            if (!activeTab.url.includes('music.youtube.com')) {
                this.updateStatus('Please open YouTube Music', 'info');
                this.updateCurrentSong(null);
                this.isConnected = false;
                return;
            }

            // Attempt to connect to content script with timeout
            const response = await this.sendMessageWithTimeout(
                activeTab.id,
                { action: 'getCurrentSong' },
                5000
            );

            if (response) {
                this.updateCurrentSong(response);
                this.isConnected = true;
                this.retryCount = 0;
            } else {
                this.handleConnectionFailure();
            }

        } catch (error) {
            console.error('Error checking current song:', error);
            this.handleConnectionFailure();
        }
    }

    /**
     * Send message with timeout promise
     * @param {number} tabId - Tab ID
     * @param {Object} message - Message to send
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise} Response or timeout
     * @private
     */
    sendMessageWithTimeout(tabId, message, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('Message timeout'));
            }, timeout);

            chrome.tabs.sendMessage(tabId, message, (response) => {
                clearTimeout(timeoutId);
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * Handle connection failures with retry logic
     * @private
     */
    handleConnectionFailure() {
        this.isConnected = false;
        this.retryCount++;

        if (this.retryCount <= this.maxRetries) {
            this.updateStatus(`Connecting... (${this.retryCount}/${this.maxRetries})`, 'info');
            setTimeout(() => this.checkCurrentSong(), 2000 * this.retryCount);
        } else {
            this.updateStatus('Connection failed. Please refresh the page.', 'error');
            this.updateCurrentSong(null);
        }
    }

    updateCurrentSong(songInfo) {
        const titleElement = document.getElementById('song-title');
        const artistElement = document.getElementById('song-artist');

        if (songInfo) {
            titleElement.textContent = songInfo.title;
            artistElement.textContent = songInfo.artist;
            this.currentSong = songInfo;
            this.updateStatus('Song detected successfully', 'success');
        } else {
            titleElement.textContent = 'No song detected';
            artistElement.textContent = 'Open YouTube Music to start';
            this.currentSong = null;
        }
    }

    async refreshLyrics() {
        if (!this.currentSong) {
            this.updateStatus('No song to refresh lyrics for', 'error');
            return;
        }

        this.showLoading(true);
        this.updateStatus('Fetching fresh lyrics...', 'info');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            chrome.tabs.sendMessage(tab.id, { 
                action: 'refreshLyrics', 
                song: this.currentSong 
            }, (response) => {
                this.showLoading(false);
                if (response && response.success) {
                    this.updateStatus('Lyrics refreshed successfully', 'success');
                } else {
                    this.updateStatus('Failed to refresh lyrics', 'error');
                }
            });
        } catch (error) {
            this.showLoading(false);
            this.updateStatus('Error refreshing lyrics', 'error');
            console.error('Refresh error:', error);
        }
    }

    showLoading(show) {
        const refreshText = document.getElementById('refresh-text');
        const refreshLoading = document.getElementById('refresh-loading');
        const refreshButton = document.getElementById('refresh-lyrics');

        if (show) {
            refreshText.classList.add('hidden');
            refreshLoading.classList.remove('hidden');
            refreshButton.disabled = true;
        } else {
            refreshText.classList.remove('hidden');
            refreshLoading.classList.add('hidden');
            refreshButton.disabled = false;
        }
    }

    updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('status');
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
        statusElement.classList.remove('hidden');

        // Auto-hide after 3 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                statusElement.classList.add('hidden');
            }, 3000);
        }
    }

    /**
     * Enhanced message sending with error handling
     */
    async sendMessageToContent(action, data = {}) {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];
            
            if (!activeTab?.url?.includes('music.youtube.com')) {
                this.updateStatus('Please open YouTube Music first', 'error');
                return;
            }

            await this.sendMessageWithTimeout(activeTab.id, { action, ...data }, 3000);
            
        } catch (error) {
            console.error('Error sending message to content script:', error);
            this.updateStatus('Communication error with page', 'error');
        }
    }

    /**
     * Load settings with validation
     */
    loadSettings() {
        try {
            chrome.storage.sync.get(['lyricsSettings'], (result) => {
                if (chrome.runtime.lastError) {
                    console.error('Settings load error:', chrome.runtime.lastError);
                    return;
                }

                if (result.lyricsSettings) {
                    this.settings = { ...this.settings, ...result.lyricsSettings };
                }
                
                this.updateUI();
            });
        } catch (error) {
            console.error('Critical error loading settings:', error);
        }
    }

    /**
     * Save settings with error handling
     */
    saveSettings() {
        try {
            chrome.storage.sync.set({ lyricsSettings: this.settings }, () => {
                if (chrome.runtime.lastError) {
                    console.error('Settings save error:', chrome.runtime.lastError);
                }
            });
        } catch (error) {
            console.error('Critical error saving settings:', error);
        }
    }

    /**
     * Update UI elements with current settings
     */
    updateUI() {
        try {
            document.getElementById('toggle-lyrics').checked = this.settings.lyricsEnabled;
            document.getElementById('toggle-autoscroll').checked = this.settings.autoScroll;
            document.getElementById('toggle-overlay').checked = this.settings.showOverlay;
        } catch (error) {
            console.error('Error updating UI:', error);
        }
    }
}

// ============================================================================
// INITIALIZATION
// ============================================================================

/**
 * Initialize popup with proper error boundaries
 */
function initializePopup() {
    try {
        const popup = new PopupController();
        console.log('Live YT Music Lyrics popup initialized');
        
        // Make available for debugging
        if (typeof window !== 'undefined') {
            window.popupController = popup;
        }
        
    } catch (error) {
        console.error('Failed to initialize popup:', error);
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePopup);
} else {
    initializePopup();
}
